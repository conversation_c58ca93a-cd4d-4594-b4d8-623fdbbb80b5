<?php

function sanitize_id($id)
{
    // Usunięcie wszystkich znaków niedozwolonych w ID (pozwalamy tylko na litery, cyfry, podkreślenia i łączniki)
    return preg_replace('/[^a-zA-Z0-9_\-]/', '', $id);
}


function transform_type($typ)
{
    // Usunięcie polskich znaków z typu
    $typ = str_replace(
        ['ą', 'ć', 'ę', 'ł', 'ń', 'ó', 'ś', 'ź', 'ż'],
        ['a', 'c', 'e', 'l', 'n', 'o', 's', 'z', 'z'],
        $typ
    );

    // Przekształcenie zmiennej typ na podstawie podanych warunków
    //!!!!!!!!!!!!!!!!!!!!!!zmienic typy dla 11 i 12
    if ($typ=="12") {
        return "MPP"; //miejsce postojowe podziemne
    } elseif ($typ=="11"){
        return "MPN"; //miejsce postojowe naziemne
    } elseif ($typ=="3"){
        return "MP"; //miejsce postojowe naziemne
    } elseif ($typ=="4"){
        return "KL";
    } elseif ($typ=="1" || $typ=="8") {
        return "";
    } else{
        return "";
    }
}



// Sprawdzanie, czy transjent 'realestate_table' istnieje
//if (false === ($output = get_transient('realestate_table'))) 
if (1) {
    // Adres URL do pliku XML
    $url = 'https://kuncer.voxdeveloper.com/webservice/realestatestatuslist/api_key/d12980b9483cc59d884415b6de20c4a24b05f3c3/investment_id/3';

    // Ładowanie pliku XML
    $xml = simplexml_load_file($url) or die("Error: Cannot create object");
    $pdf_image_url = plugin_dir_url(__FILE__) . '../images/pdf_1.png';




    // Tworzenie początku tabeli
    $output = '<div id="system_content">';

    // Przeszukiwanie XML
    foreach ($xml->realestate as $realestate) {
        $local_number = $realestate->local_number;
        $price = format_number($realestate->price);
        //echo "<script> console.log(" . $local_number . ");</script>";

        /* Dostepne statusy
            1 - Dostępne
            2 - Rezerwacja ustna
            3 - Umowa rezerwacyjna
            4 - Sprzedane
            5 - Umowa przedwstępna
            6 - Umowa deweloperska
            7 - Akt notarialny
            8 - Odbiór
            9 - Wstrzymany 
            */
        switch ($realestate->status_id) {
            case "1":
                $status = "status1";
                break;
            case "2":
                $status = "status2";
                break;
            case "3":
                $status = "status2";
                break;
            case "4":
                $status = "status3";
                break;
            case "5":
                $status = "status3";
                break;
            case "6":
                $status = "status3";
                break;
            case "7":
                $status = "status3";
                break;
            case "8":
                $status = "status3";
                break;
            case "9":
                $status = "status3";
                break;

            default:
                $status = "status1";
                break;
        }

        // Reszta Twojego kodu
        //$status = 'status' . $realestate->status_id;
        $area = $realestate->area;
        $card_link = $realestate->card_link;
        $rooms = $realestate->rooms;
        $extras = $realestate->description != "" ? " + " . $realestate->description : '';
        $balkon = $realestate->balkon;
        $taras = $realestate->taras;
        $ogrod = $realestate->ogrod;
        $loggia = $realestate->loggia;
        $antresole_area = $realestate->antresole_area;
        $budynek = $realestate->building;
        $pietro = $realestate->floor;
        $typ = $realestate->type;
        $typ_id = $realestate->type_id;

        $typ = transform_type($typ_id);

        $extra_desc = $typ == "MP" ? "MP" : "";

        // Stworzenie kopii $local_number do przekształcenia
        $modified_local_number = $local_number;

        if (strpos($modified_local_number, '+') !== false) {
            // Pobierz wszystko przed '+'
            $modified_local_number = substr($modified_local_number, 0, strpos($modified_local_number, '+'));
            // Usuń spacje
            $modified_local_number = str_replace(' ', '', $modified_local_number);
        }

        // Generowanie dodatkowych linii opisowych
        $extra_features = "";
        if ($balkon > 0) {
            $extra_features .= 'Balkon: <strong>' . $balkon . ' m<sup>2</sup></strong><br>';
        }
        if ($taras > 0) {
            $extra_features .= 'Taras: <strong>' . $taras . ' m<sup>2</sup></strong><br>';
        }
        if ($ogrod > 0) {
            $extra_features .= 'Ogród: <strong>' . $ogrod . ' m<sup>2</sup></strong><br>';
        }
        if ($loggia > 0) {
            $extra_features .= 'Loggia: <strong>' . $loggia . ' m<sup>2</sup></strong><br>';
        }
        if ($antresole_area > 0) {
            $extra_features .= 'Antresola: <strong>' . $antresole_area . ' m<sup>2</sup></strong><br>';
        }

        $room_info = '';
        if ($rooms > 0) {
            $room_info = 'Ilość pokoi: <strong>' . $rooms . $extras . '</strong><br>';
        }

        $output .= '
                <div id="t' . $typ . '' . sanitize_id($modified_local_number) . '" data-type-id="' . $typ_id . '" data-local="' . $realestate->local_number . '" data-budynek="' . $budynek . '" data-area="' . $area . '" data-pietro="' . $pietro . '" class="apartment typ_' . $typ . ' ' . $status . '">
                    <div class="system_col_1_4">
                        <p style="text-align: center;">' . $extra_desc . $local_number . '</p>
                    </div>
                    <div class="system_col_2_4">
                        <h5 style="text-align: center;"><strong>Zestawienie pomieszczeń</strong></h5>
                        <p class="system_description" style="text-align: center;">
                            Powierzchnia: <strong>' . $area . ' m<sup>2</sup></strong><br>
                            ' . $room_info . '
                            ' . $extra_features . '
                        </p>
                    </div>
                    <div class="system_col_3_4">
                        <h5 style="text-align: center;"><strong>Cena brutto</strong></h5>
                        <p class="price">' . $price . ' zł</p>
                    </div>
                    <div class="system_col_4_4">
                        <h5 style="text-align: center;"><strong>Karta lokalu</strong></h5>
                        <p style="text-align: center;">
                            <a href="' . $card_link . '" target="_blank" rel="noopener noreferrer" title="Karta lokalu ' . $local_number . '">
                                <img decoding="async" src="' . $pdf_image_url . '" width="64" height="64">
                            </a>
                        </p>
                    </div>
                </div>';
    }



    // Zakończenie tabeli
    $output .= '</div>';

    // Ustawianie transientu 'realestate_table' na 1 godzinę
    set_transient('realestate_table', $output, 1 * HOUR_IN_SECONDS);
}
